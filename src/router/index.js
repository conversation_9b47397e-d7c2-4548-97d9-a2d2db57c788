import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },

  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      },

    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addMaternitySuite',
        component: () => import('@/views/config/addMaternitySuite'),
        name: 'addMaternitySuite',
        meta: { title: '新增房型', activeMenu: '/suite/home/<USER>' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addtopicManagement',
        component: () => import('@/views/config/addtopicManagement'),
        name: 'addtopicManagement',
        meta: { title: '新增话题', activeMenu: '/community/config' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addnursingStaff',
        component: () => import('@/views/config/addnursingStaff'),
        name: 'addnursingStaff',
        meta: { title: '新增人员', activeMenu: '/suite/home/<USER>' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addpostBirth',
        component: () => import('@/views/config/addpostBirth'),
        name: 'addpostBirth',
        meta: { title: '新增产后康复', activeMenu: '/suite/home/<USER>' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addinformationCenter',
        component: () => import('@/views/config/addinformationCenter'),
        name: 'addinformationCenter',
        meta: { title: '新增客户', activeMenu: '/customerInformation/Customer' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addCombo',
        component: () => import('@/views/config/addCombo'),
        name: 'addCombo',
        meta: { title: '优惠套餐配置', activeMenu: '/commodity/discountPackage' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addroomManagement',
        component: () => import('@/views/config/addroomManagement'),
        name: 'addroomManagement',
        meta: { title: '房间管理', activeMenu: '/roomManagement' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/backlog',
        component: () => import('@/views/config/backlog'),
        name: 'backlog',
        meta: { title: '待办事项', activeMenu: '/roomManagement' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/history',
        component: () => import('@/views/config/history'),
        name: 'history',
        meta: { title: '历史记录', activeMenu: '/roomManagement' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/historyDetails',
        component: () => import('@/views/config/historyDetails'),
        name: 'historyDetails',
        meta: { title: '历史详情', activeMenu: '/roomManagement' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/indexConfig',
        component: () => import('@/views/config/indexConfig'),
        name: 'indexConfig',
        meta: { title: '主页配置', activeMenu: '/suite/home/<USER>' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/monthlyDiet',
        component: () => import('@/views/config/monthlyDiet'),
        name: 'monthlyDiet',
        meta: { title: '月子膳食配置', activeMenu: '/suite/home/<USER>' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/clubhouseFacilities',
        component: () => import('@/views/config/clubhouseFacilities'),
        name: 'clubhouseFacilities',
        meta: { title: '会所设置配置', activeMenu: '/suite/home/<USER>' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/dataAnalysisDetails',
        component: () => import('@/views/config/dataAnalysisDetails'),
        name: 'dataAnalysisDetails',
        meta: { title: '数据分析/用户详情', activeMenu: '/dataAnalysis' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/totalAccess',
        component: () => import('@/views/config/totalAccess'),
        name: 'totalAccess',
        meta: { title: '数据分析/总访用户量', activeMenu: '/dataAnalysis' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/totalBrowsingTime',
        component: () => import('@/views/config/totalBrowsingTime'),
        name: 'totalBrowsingTime',
        meta: { title: '数据分析/浏览总时间', activeMenu: '/dataAnalysis' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/totalBrowsingTimeDetails',
        component: () => import('@/views/config/totalBrowsingTimeDetails'),
        name: 'totalBrowsingTimeDetails',
        meta: { title: '数据分析/浏览总时间/房间模块', activeMenu: '/dataAnalysis' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/totalVisits',
        component: () => import('@/views/config/totalVisits'),
        name: 'totalVisits',
        meta: { title: '数据分析/总访问次数', activeMenu: '/dataAnalysis' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/perinatalDataDetails',
        component: () => import('@/views/config/perinatalDataDetails'),
        name: 'perinatalDataDetails',
        meta: { title: '预产期数据/用户详情', activeMenu: '/perinatalData' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/addPregnancyAssistant',
        component: () => import('@/views/config/addPregnancyAssistant'),
        name: 'addPregnancyAssistant',
        meta: { title: '孕期助手/新增', activeMenu: '/suite/home/<USER>' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/suppliesManagement',
        component: () => import('@/views/config/suppliesManagement'),
        name: 'suppliesManagement',
        meta: { title: '用品管理', activeMenu: '/commodity/suppliesManagement' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/nursingProject',
        component: () => import('@/views/config/nursingProject'),
        name: 'nursingProject',
        meta: { title: '护理项目管理', activeMenu: '/commodity/nursingProject' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/informationCenter',
        component: () => import('@/views/config/informationCenter'),
        name: 'informationCenter',
        meta: { title: '客户管理', activeMenu: '/customerInformation/Customer' }
      }
    ]
  },
  {
    path: '/wechat',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: '/merchants',
        component: () => import('@/views/system/wechat/merchants/index'),
        name: 'Merchants',
        meta: { title: '小程序商家配置', activeMenu: '/wechat/MiniProgram' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/labelManagementUpdate',
        component: () => import('@/views/config/labelManagementUpdate'),
        name: 'labelManagementUpdate',
        meta: { title: '标签修改', activeMenu: '/labelManagement' }
      }
    ]
  },
  {
    path: '/employee',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/detail',
        component: () => import('@/views/platform/employee/home/<USER>'),
        name: 'EmployeeDetail',
        meta: { title: '员工详情', activeMenu: '/employee/home' }
      }
    ]
  },
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [

  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/ai/knowledge',
    component: Layout,
    permissions: ['ai:knowledgeBase:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/ai/knowledge/index'),
        name: 'KnowledgeBase',
        meta: { title: 'AI知识库管理', icon: 'el-icon-collection' }
      }
    ]
  },
  {
    path: '/ai/knowledge',
    component: Layout,
    hidden: true,
    permissions: ['ai:knowledgeBase:query'],
    children: [
      {
        path: 'detail/:id(\\d+)',
        component: () => import('@/views/ai/knowledge/detail'),
        name: 'KnowledgeBaseDetail',
        meta: { title: '知识库详情', activeMenu: '/ai/knowledge' }
      }
    ]
  },
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
