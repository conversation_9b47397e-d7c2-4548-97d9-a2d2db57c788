import request from '@/utils/request'

// 查询角色列表
export function getRoleList(params) {
    return request({
        url: '/platform/employee/role_list',
        method: 'get',
        params: params
    })
}

// 分页查询角色列表
export function getRolePage(params) {
    return request({
        url: '/platform/role/page',
        method: 'get',
        params: params
    })
}

// 新增角色
export function createRole(data) {
    return request({
        url: '/platform/role/save',
        method: 'post',
        data: data
    })
}

// 修改角色
export function updateRole(data) {
    return request({
        url: '/platform/role/update',
        method: 'post',
        data: data
    })
}

// 删除角色
export function deleteRole(id) {
    return request({
        url: `/platform/role/remove/${id}`,
        method: 'delete'
    })
}

// 查询角色详情
export function getRoleDetail(id) {
    return request({
        url: `/platform/role/info/${id}`,
        method: 'get'
    })
}

// 查询微信小程序角色列表
export function getMpRoleList(params) {
    return request({
        url: '/mp/merchant/role_list',
        method: 'get',
        params: params
    })
}
